/**
 * Frontend JavaScript for Amlak Auctions Block
 * Handles dynamic loading and rendering of auction data
 */

document.addEventListener('DOMContentLoaded', function() {
    // Find all auction blocks on the page
    const auctionBlocks = document.querySelectorAll('.wp-block-create-block-amlak-auctions-block');

    auctionBlocks.forEach(block => {
        loadAuctionsForBlock(block);
    });
});

async function loadAuctionsForBlock(blockElement) {
    try {
        // Get block attributes from data attributes
        const attributes = {
            columns: parseInt(blockElement.dataset.columns) || 3,
            auctionsCount: parseInt(blockElement.dataset.auctionsCount) || 6,
            showImages: blockElement.dataset.showImages === 'true',
            showTitle: blockElement.dataset.showTitle === 'true',
            showType: blockElement.dataset.showType === 'true',
            showLocation: blockElement.dataset.showLocation === 'true',
            showAssetsCount: blockElement.dataset.showAssetsCount === 'true',
            showDateTime: blockElement.dataset.showDateTime === 'true',
            showCountdown: blockElement.dataset.showCountdown === 'true',
            showAgent: blockElement.dataset.showAgent === 'true'
        };

        // Fetch auctions data
        const response = await fetch(`/wp-json/amlak/v1/auctions?limit=${attributes.auctionsCount}&status=publish`);

        if (!response.ok) {
            throw new Error('Failed to fetch auctions');
        }

        const auctions = await response.json();

        // Render auctions
        renderAuctions(blockElement, auctions, attributes);

    } catch (error) {
        console.error('Error loading auctions:', error);
        blockElement.innerHTML = `
            <div class="amlak-auctions-error">
                <p>حدث خطأ في تحميل المزادات</p>
                <button onclick="location.reload()">إعادة المحاولة</button>
            </div>
        `;
    }
}

function renderAuctions(blockElement, auctions, attributes) {
    if (!auctions || auctions.length === 0) {
        blockElement.innerHTML = `
            <div class="amlak-auctions-empty">
                <p>لا توجد مزادات متاحة حالياً</p>
            </div>
        `;
        return;
    }

    const gridHTML = `
        <div class="amlak-auctions-grid" style="display: grid; grid-template-columns: repeat(${attributes.columns}, 1fr); gap: 24px;">
            ${auctions.map(auction => renderAuctionCard(auction, attributes)).join('')}
        </div>
    `;

    blockElement.innerHTML = gridHTML;

    // Initialize countdowns after rendering
    initializeCountdowns(blockElement);
}

function renderAuctionCard(auction, settings) {
    const auctionTypeIcon = {
        'إلكتروني': '<svg width="22" height="22" viewBox="0 0 24 24" fill="white"><rect x="3" y="6" width="18" height="9" rx="2"/><rect x="7" y="16" width="10" height="2" rx="1"/></svg>',
        'حضوري': '<svg width="22" height="22" viewBox="0 0 24 24" fill="white"><path d="M16 11c1.66 0 2.99-1.34 2.99-3S17.66 5 16 5s-3 1.34-3 3 1.34 3 3 3zm-8 0c1.66 0 2.99-1.34 2.99-3S9.66 5 8 5 5 6.34 5 8s1.34 3 3 3zm0 2c-2.33 0-7 1.17-7 3.5V19h14v-2.5c0-2.33-4.67-3.5-7-3.5zm8 0c-.29 0-.62.02-.97.05C15.64 13.36 17 14.28 17 15.5V19h6v-2.5c0-2.33-4.67-3.5-7-3.5z"/></svg>',
        'هجين': '<svg width="22" height="22" viewBox="0 0 24 24" fill="white"><path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/></svg>'
    };

    const imagesHTML = settings.showImages && auction.images && auction.images.length > 0 ? `
        <div class="auction-img-wrapper">
            ${settings.showType ? `
                <span class="auction-type-badge auction-type-${auction.type}">
                    <span class="badge-icon-bg badge-icon-bg-${auction.type}">
                        <span class="badge-icon-large">${auctionTypeIcon[auction.type] || ''}</span>
                    </span>
                    <span class="badge-text-bg">${auction.type}</span>
                </span>
            ` : ''}
            <div class="img-slider">
                <img src="${auction.images[0]}" alt="${auction.title}" style="width: 100%; height: 200px; object-fit: cover; border-radius: 12px 12px 0 0;">
            </div>
        </div>
    ` : '';

    const titleAgentHTML = settings.showTitle ? `
        <div class="auction-title-agent-row">
            <h2 class="auction-title-new">${auction.title}</h2>
            ${settings.showAgent && auction.agent ? `<div class="auction-agent-new">${auction.agent}</div>` : ''}
        </div>
    ` : '';

    const locationHTML = settings.showLocation && auction.location ? `
        <div class="auction-info-item">
            <span class="icon">
                <svg width="24" height="24" fill="#BB1919" viewBox="0 0 24 24"><path d="M12 2C8.13 2 5 5.13 5 9c0 5.25 7 13 7 13s7-7.75 7-13c0-3.87-3.13-7-7-7zm0 11.5A2.5 2.5 0 1112 8a2.5 2.5 0 010 5.5z"/></svg>
            </span>
            ${auction.location}
        </div>
    ` : '';

    const assetsCountHTML = settings.showAssetsCount ? `
        <div class="auction-info-item">
            <span class="icon">
                <svg width="24" height="24" fill="#BB1919" viewBox="0 0 24 24"><path d="M4 22V2h16v20h-7v-6h-2v6H4zm2-2h4v-6h4v6h4V4H6v16z"/></svg>
            </span>
            ${auction.assetsCount} ${auction.assetsCount === 1 ? 'أصل' : 'أصول'}
        </div>
    ` : '';

    const dateTimeHTML = settings.showDateTime && auction.date ? `
        <div class="auction-date-row">
            <span class="icon">
                <svg width="24" height="24" fill="#BB1919" viewBox="0 0 24 24"><path d="M19 4h-1V2h-2v2H8V2H6v2H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V6c0-1.1-.9-2-2-2zm0 16H5V9h14v11zm0-13H5V6h14v1zm-7 5h5v5h-5z"/></svg>
            </span>
            <span class="auction-date-text">${formatAuctionDateTime(auction.date, auction.time)}</span>
        </div>
    ` : '';

    const countdownHTML = settings.showCountdown && auction.endDateTime ? `
        <div class="countdown-container" data-end-time="${auction.endDateTime}">
            <div class="countdown-loading">جاري تحميل العد التنازلي...</div>
        </div>
    ` : '';

    return `
        <div class="amlak-auction-card">
            ${imagesHTML}
            ${titleAgentHTML}
            <div class="auction-info-grid">
                ${locationHTML}
                ${assetsCountHTML}
                ${dateTimeHTML}
            </div>
            ${countdownHTML}
        </div>
    `;
}

function formatAuctionDateTime(date, time) {
    if (!date) return '';

    const months = [
        'يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو',
        'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'
    ];

    const dateObj = new Date(date);
    const day = dateObj.getDate();
    const month = months[dateObj.getMonth()];
    const year = dateObj.getFullYear();

    return `${day} ${month} ${year}${time ? ` - ${time}` : ''}`;
}

function initializeCountdowns(blockElement) {
    const countdowns = blockElement.querySelectorAll('.countdown-container');

    countdowns.forEach(countdown => {
        const endTime = countdown.dataset.endTime;
        if (endTime) {
            updateCountdown(countdown, endTime);
            // Update every second
            setInterval(() => updateCountdown(countdown, endTime), 1000);
        }
    });
}

function updateCountdown(element, endTime) {
    const now = new Date().getTime();
    const end = new Date(endTime).getTime();
    const distance = end - now;

    if (distance < 0) {
        element.innerHTML = '<div class="countdown-expired">انتهى المزاد</div>';
        return;
    }

    const days = Math.floor(distance / (1000 * 60 * 60 * 24));
    const hours = Math.floor((distance % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
    const minutes = Math.floor((distance % (1000 * 60 * 60)) / (1000 * 60));
    const seconds = Math.floor((distance % (1000 * 60)) / 1000);

    element.innerHTML = `
        <div class="countdown-timer">
            <div class="countdown-item">
                <span class="countdown-number">${days}</span>
                <span class="countdown-label">يوم</span>
            </div>
            <div class="countdown-item">
                <span class="countdown-number">${hours}</span>
                <span class="countdown-label">ساعة</span>
            </div>
            <div class="countdown-item">
                <span class="countdown-number">${minutes}</span>
                <span class="countdown-label">دقيقة</span>
            </div>
            <div class="countdown-item">
                <span class="countdown-number">${seconds}</span>
                <span class="countdown-label">ثانية</span>
            </div>
        </div>
    `;
}
