import React, { useEffect, useState } from 'react';
import { InspectorControls, useBlockProps } from '@wordpress/block-editor';
import Card from './Card';
import SettingsPanel from './SettingsPanel';
import './style.scss';

const mockAuctions = [
    {
        id: 1,
        title: 'مزاد الرياض للعقارات',
        type: 'إلكتروني',
        location: 'الرياض - العليا',
        assetsCount: 5,
        date: '2025-06-30',
        time: '17:39',
        endDateTime: '2025-06-30T17:39:00',
        agent: 'شركة أملـاك',
        images: [
            'https://images.unsplash.com/photo-1506744038136-46273834b3fb?auto=format&fit=crop&w=400&q=80',
            'https://images.unsplash.com/photo-1465101046530-73398c7f28ca?auto=format&fit=crop&w=400&q=80',
        ],
    },
    {
        id: 2,
        title: 'مز<PERSON> جدة للأراضي',
        type: 'حضوري',
        location: 'جدة - التحلية',
        assetsCount: 3,
        date: '2025-07-10',
        time: '15:00',
        endDateTime: '2025-07-10T15:00:00',
        agent: 'مؤسسة جدة',
        images: [
            'https://images.unsplash.com/photo-1512918728675-ed5a9ecdebfd?auto=format&fit=crop&w=400&q=80',
            'https://images.unsplash.com/photo-1523217582562-09d0def993a6?auto=format&fit=crop&w=400&q=80',
        ],
    },
    {
        id: 3,
        title: 'مزاد الدمام للفلل',
        type: 'هجين',
        location: 'الدمام - الخليج',
        assetsCount: 7,
        date: '2025-08-15',
        time: '18:30',
        endDateTime: '2025-08-15T18:30:00',
        agent: 'وكالة الشرقية',
        images: [
            'https://images.unsplash.com/photo-1468436139062-f60a71c5c892?auto=format&fit=crop&w=400&q=80',
            'https://images.unsplash.com/photo-1507089947368-19c1da9775ae?auto=format&fit=crop&w=400&q=80',
        ],
    },
];

const AuctionsGrid = ({ auctions, columns, settings }) => (
    <div
        className="amlak-auctions-grid"
        style={{
            display: 'grid',
            gridTemplateColumns: `repeat(${columns}, 1fr)`,
            gap: '24px',
        }}
    >
        {auctions.map((auction) => (
            <Card key={auction.id} auction={auction} settings={settings} />
        ))}
    </div>
);

export default function Edit({ attributes, setAttributes }) {
    // لاحقاً: استبدل mockAuctions بجلب فعلي من REST API
    const [auctions, setAuctions] = useState(mockAuctions.slice(0, attributes.auctionsCount));

    useEffect(() => {
        setAuctions(mockAuctions.slice(0, attributes.auctionsCount));
    }, [attributes.auctionsCount]);

    return (
        <div {...useBlockProps()}>
            <InspectorControls>
                <SettingsPanel attributes={attributes} setAttributes={setAttributes} />
            </InspectorControls>
            <AuctionsGrid auctions={auctions} columns={attributes.columns} settings={attributes} />
        </div>
    );
}
