/*!********************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/css-loader/dist/cjs.js??ruleSet[1].rules[2].use[1]!./node_modules/postcss-loader/dist/cjs.js??ruleSet[1].rules[2].use[2]!./src/amlak-auctions-block/ImgSlider.css ***!
  \********************************************************************************************************************************************************************************************/
.amlak-img-slider {
  position: relative;
  text-align: center;
  overflow: visible;
}
.slider-img-fade-wrapper {
  position: relative;
  width: 100%;
  min-height: 180px;
  max-height: 200px;
  z-index: 1;
}
.slider-img-fade {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  -o-object-fit: cover;
     object-fit: cover;
  opacity: 0;
  transition: opacity 0.7s cubic-bezier(.4,0,.2,1);
  border-radius: 8px;
  z-index: 1;
  pointer-events: none;
}
.slider-img-fade.active {
  opacity: 1;
  z-index: 2;
  pointer-events: auto;
}
.slider-dots {
  position: absolute;
  left: 50%;
  bottom: 10px;
  transform: translateX(-50%);
  z-index: 10;
  display: flex;
  gap: 6px;
}
.slider-img-fade-wrapper {
  position: relative;
  width: 100%;
  min-height: 180px;
  max-height: 200px;
}
.slider-img-fade {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  -o-object-fit: cover;
     object-fit: cover;
  opacity: 0;
  transition: opacity 0.7s cubic-bezier(.4,0,.2,1);
  border-radius: 8px;
  z-index: 1;
  pointer-events: none;
}
.slider-img-fade.active {
  opacity: 1;
  z-index: 2;
  pointer-events: auto;
}
.slider-btn {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  background: #fff;
  border: none;
  font-size: 1.5em;
  cursor: pointer;
  z-index: 2;
  padding: 0 10px;
  border-radius: 50%;
  box-shadow: 0 2px 6px rgba(0,0,0,0.1);
}
.slider-btn.prev { left: 10px; }
.slider-btn.next { right: 10px; }
.slider-dots {
  margin-top: 8px;
}
.dot {
  display: inline-block;
  width: 10px;
  height: 10px;
  margin: 0 3px;
  background: #ccc;
  border-radius: 50%;
  cursor: pointer;
}
.dot.active {
  background: #007cba;
}

/*!********************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/css-loader/dist/cjs.js??ruleSet[1].rules[2].use[1]!./node_modules/postcss-loader/dist/cjs.js??ruleSet[1].rules[2].use[2]!./src/amlak-auctions-block/Countdown.css ***!
  \********************************************************************************************************************************************************************************************/
.amlak-countdown-cards-row {
  display: flex;
  align-items: center;
  gap: 10px;
  justify-content: center;
  margin: 14px 0 0 0;
}
.countdown-rects {
  display: flex;
  gap: 10px;
}
.countdown-rect {
  background: rgba(30,30,30,0.08);
  border-radius: 8px;
  min-width: 48px;
  min-height: 48px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  box-shadow: 0 1px 4px rgba(0,0,0,0.08);
  border: 1.5px solid #e0e0e0;
  padding: 6px 10px 4px 10px;
}
.countdown-num {
  font-size: 1.25em;
  font-weight: bold;
  color: #007cba;
  margin-bottom: 2px;
}
.countdown-label {
  font-size: 0.85em;
  color: #666;
  font-weight: 500;
}

/*!***************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/css-loader/dist/cjs.js??ruleSet[1].rules[2].use[1]!./node_modules/postcss-loader/dist/cjs.js??ruleSet[1].rules[2].use[2]!./src/amlak-auctions-block/Card.css ***!
  \***************************************************************************************************************************************************************************************/
.amlak-auction-card {
  background: #fff;
  border-radius: 16px;
  box-shadow: 0 4px 24px rgba(0,0,0,0.10);
  padding: 0 0 18px 0;
  display: flex;
  flex-direction: column;
  gap: 10px;
  align-items: stretch;
  min-height: 370px;
  transition: box-shadow 0.2s;
  overflow: hidden;
  border: 1px solid #f0f0f0;
  direction: rtl;
}
.amlak-auction-card:hover {
  box-shadow: 0 8px 32px rgba(0,0,0,0.16);
  border-color: #e0e0e0;
}
.auction-title-agent-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 14px 18px 8px 18px;
  margin: 0;
  direction: rtl;
}
.auction-title-new {
  font-size: 1.18em;
  font-weight: bold;
  color: #1a2233;
  line-height: 1.3;
  margin: 0;
  max-width: 60%;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.auction-agent-new {
  background: #BB1919;
  color: #fff;
  font-size: 0.98em;
  font-weight: 600;
  padding: 2px 12px;
  border-radius: 6px;
  display: inline-block;
  letter-spacing: 0.01em;
  max-width: 35%;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.auction-type, .auction-location, .auction-assets-count, .auction-date-time, .auction-agent {
  font-size: 0.97em;
  color: #555;
  margin: 0 16px 2px 16px;
  line-height: 1.7;
}
.amlak-img-slider {
  border-radius: 16px 16px 0 0;
  overflow: hidden;
  min-height: 180px;
}
.amlak-auction-card .slider-img {
  min-height: 180px;
  max-height: 200px;
  -o-object-fit: cover;
     object-fit: cover;
  width: 100%;
}
.amlak-countdown {
  margin: 8px 16px 0 16px;
}
.amlak-auction-card .auction-btn {
  margin: 16px;
  margin-top: 10px;
  background: #007cba;
  color: #fff;
  border: none;
  border-radius: 6px;
  padding: 8px 0;
  font-size: 1em;
  font-weight: 500;
  cursor: pointer;
  transition: background 0.2s;
  width: 100%;
}
.amlak-auction-card .auction-btn:hover {
  background: #005fa3;
}

.auction-img-wrapper {
  position: relative;
}
.auction-type-badge {
  position: absolute;
  top: 10px;
  right: 10px;
  display: flex;
  align-items: stretch;
  border-radius: 8px;
  z-index: 2;
  box-shadow: none;
  border: none;
  overflow: hidden;
}
.badge-icon-bg {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 12px;
}
.badge-icon-bg-إلكتروني { background: #2196f3; }
.badge-icon-bg-حضوري { background: #43a047; }
.badge-icon-bg-هجين { background: #8e44ad; }
.badge-icon-large {
  width: 28px;
  height: 28px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.badge-icon-large svg {
  width: 22px;
  height: 22px;
  display: block;
  color: #fff;
  fill: #fff;
}
.badge-text-bg {
  background: #f7f7f7;
  color: #222;
  font-size: 1.08em;
  font-weight: 600;
  display: flex;
  align-items: center;
  padding: 0 16px 0 12px;
  letter-spacing: 0.01em;
}
.auction-info-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 8px 10px;
  margin: 10px 16px 0 16px;
}
.auction-info-item {
  display: flex;
  align-items: center;
  font-size: 0.97em;
  color: #444;
  background: #f8fafd;
  border-radius: 6px;
  padding: 6px 10px 6px 6px;
  gap: 6px;
  min-width: 0;
  word-break: break-word;
}
.auction-info-item .icon {
  display: flex;
  align-items: center;
  margin-left: 4px;
}


/*# sourceMappingURL=index.css.map*/