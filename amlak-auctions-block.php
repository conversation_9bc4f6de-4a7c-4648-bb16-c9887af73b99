<?php
/**
 * Plugin Name:       Amlak Auctions Block
 * Description:       WordPress block for displaying real estate auctions with full database integration.
 * Version:           0.1.0
 * Requires at least: 6.7
 * Requires PHP:      7.4
 * Author:            <PERSON>
 * License:           GPL-2.0-or-later
 * License URI:       https://www.gnu.org/licenses/gpl-2.0.html
 * Text Domain:       amlak-auctions-block
 *
 * @package AmlakAuctionsBlock
 */

if ( ! defined( 'ABSPATH' ) ) {
	exit; // Exit if accessed directly.
}

/**
 * Registers the block using the metadata loaded from the `block.json` file.
 * Behind the scenes, it registers also all assets so they can be enqueued
 * through the block editor in the corresponding context.
 *
 * @see https://developer.wordpress.org/reference/functions/register_block_type/
 */
function create_block_amlak_auctions_block_block_init() {
	register_block_type( __DIR__ . '/build/amlak-auctions-block' );
}
add_action( 'init', 'create_block_amlak_auctions_block_block_init' );

/**
 * Enqueue frontend styles and scripts
 */
function amlak_auctions_enqueue_frontend_assets() {
	// Only enqueue on pages that contain our block
	if ( has_block( 'create-block/amlak-auctions-block' ) ) {
		wp_enqueue_style(
			'amlak-auctions-frontend-style',
			plugin_dir_url( __FILE__ ) . 'build/amlak-auctions-block/style-index.css',
			array(),
			filemtime( plugin_dir_path( __FILE__ ) . 'build/amlak-auctions-block/style-index.css' )
		);

		wp_enqueue_script(
			'amlak-auctions-frontend-script',
			plugin_dir_url( __FILE__ ) . 'build/amlak-auctions-block/view.js',
			array(),
			filemtime( plugin_dir_path( __FILE__ ) . 'build/amlak-auctions-block/view.js' ),
			true
		);
	}
}
add_action( 'wp_enqueue_scripts', 'amlak_auctions_enqueue_frontend_assets' );

/**
 * Register REST API endpoint for fetching auctions
 */
function amlak_auctions_register_rest_routes() {
	register_rest_route( 'amlak/v1', '/auctions', array(
		'methods'  => 'GET',
		'callback' => 'amlak_get_auctions_data',
		'permission_callback' => '__return_true',
		'args' => array(
			'limit' => array(
				'default' => 6,
				'sanitize_callback' => 'absint',
			),
			'status' => array(
				'default' => 'publish',
				'sanitize_callback' => 'sanitize_text_field',
			),
		),
	) );
}
add_action( 'rest_api_init', 'amlak_auctions_register_rest_routes' );

/**
 * Get auctions data from database
 */
function amlak_get_auctions_data( $request ) {
	global $wpdb;

	$limit = $request->get_param( 'limit' );
	$status = $request->get_param( 'status' );

	// Get auctions from wp_posts
	$auctions_query = $wpdb->prepare(
		"SELECT ID, post_title, post_content, post_date
		FROM {$wpdb->posts}
		WHERE post_type = 'auction'
		AND post_status = %s
		ORDER BY post_date DESC
		LIMIT %d",
		$status,
		$limit
	);

	$auctions = $wpdb->get_results( $auctions_query );

	if ( empty( $auctions ) ) {
		return new WP_REST_Response( array(), 200 );
	}

	$formatted_auctions = array();

	foreach ( $auctions as $auction ) {
		// Get auction metadata
		$meta_data = get_post_meta( $auction->ID );

		// Get auction assets count and images
		$assets_query = $wpdb->prepare(
			"SELECT COUNT(*) as assets_count, GROUP_CONCAT(images) as all_images
			FROM {$wpdb->prefix}auction_assets
			WHERE auction_id = %d",
			$auction->ID
		);

		$assets_data = $wpdb->get_row( $assets_query );

		// Process images from assets
		$images = array();
		if ( ! empty( $assets_data->all_images ) ) {
			$all_images_json = explode( ',', $assets_data->all_images );
			foreach ( $all_images_json as $image_json ) {
				if ( ! empty( $image_json ) ) {
					$decoded_images = json_decode( $image_json, true );
					if ( is_array( $decoded_images ) ) {
						$images = array_merge( $images, $decoded_images );
					}
				}
			}
			// Remove duplicates and limit to first 3 images
			$images = array_unique( $images );
			$images = array_slice( $images, 0, 3 );
		}

		// Map auction type
		$auction_type_map = array(
			'online' => 'إلكتروني',
			'offline' => 'حضوري',
			'hybrid' => 'هجين'
		);

		$auction_type = isset( $meta_data['_auction_type'][0] ) ? $meta_data['_auction_type'][0] : 'online';
		$auction_type_ar = isset( $auction_type_map[ $auction_type ] ) ? $auction_type_map[ $auction_type ] : 'إلكتروني';

		// Format date and time
		$auction_date = isset( $meta_data['_auction_date'][0] ) ? $meta_data['_auction_date'][0] : '';
		$auction_time = isset( $meta_data['_auction_time'][0] ) ? $meta_data['_auction_time'][0] : '';
		$end_date_time = '';

		if ( ! empty( $auction_date ) && ! empty( $auction_time ) ) {
			$end_date_time = $auction_date . 'T' . $auction_time . ':00';
		}

		// Get location info
		$auction_city = isset( $meta_data['_auction_city'][0] ) ? $meta_data['_auction_city'][0] : '';
		$location = $auction_city;

		// Add formatted auction to array
		$formatted_auctions[] = array(
			'id' => $auction->ID,
			'title' => $auction->post_title,
			'type' => $auction_type_ar,
			'location' => $location,
			'assetsCount' => (int) $assets_data->assets_count,
			'date' => $auction_date,
			'time' => $auction_time,
			'endDateTime' => $end_date_time,
			'agent' => isset( $meta_data['_company_name'][0] ) ? $meta_data['_company_name'][0] : '',
			'images' => $images,
			'description' => $auction->post_content,
		);
	}

	return new WP_REST_Response( $formatted_auctions, 200 );
}
