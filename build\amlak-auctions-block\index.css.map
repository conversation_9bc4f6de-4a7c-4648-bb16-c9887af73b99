{"version": 3, "file": "amlak-auctions-block/index.css", "mappings": ";;;AAAA;EACE,kBAAkB;EAClB,kBAAkB;EAClB,iBAAiB;AACnB;AACA;EACE,kBAAkB;EAClB,WAAW;EACX,iBAAiB;EACjB,iBAAiB;EACjB,UAAU;AACZ;AACA;EACE,kBAAkB;EAClB,MAAM;EACN,OAAO;EACP,WAAW;EACX,YAAY;EACZ,oBAAiB;KAAjB,iBAAiB;EACjB,UAAU;EACV,gDAAgD;EAChD,kBAAkB;EAClB,UAAU;EACV,oBAAoB;AACtB;AACA;EACE,UAAU;EACV,UAAU;EACV,oBAAoB;AACtB;AACA;EACE,kBAAkB;EAClB,SAAS;EACT,YAAY;EACZ,2BAA2B;EAC3B,WAAW;EACX,aAAa;EACb,QAAQ;AACV;AACA;EACE,kBAAkB;EAClB,WAAW;EACX,iBAAiB;EACjB,iBAAiB;AACnB;AACA;EACE,kBAAkB;EAClB,MAAM;EACN,OAAO;EACP,WAAW;EACX,YAAY;EACZ,oBAAiB;KAAjB,iBAAiB;EACjB,UAAU;EACV,gDAAgD;EAChD,kBAAkB;EAClB,UAAU;EACV,oBAAoB;AACtB;AACA;EACE,UAAU;EACV,UAAU;EACV,oBAAoB;AACtB;AACA;EACE,kBAAkB;EAClB,QAAQ;EACR,2BAA2B;EAC3B,gBAAgB;EAChB,YAAY;EACZ,gBAAgB;EAChB,eAAe;EACf,UAAU;EACV,eAAe;EACf,kBAAkB;EAClB,qCAAqC;AACvC;AACA,mBAAmB,UAAU,EAAE;AAC/B,mBAAmB,WAAW,EAAE;AAChC;EACE,eAAe;AACjB;AACA;EACE,qBAAqB;EACrB,WAAW;EACX,YAAY;EACZ,aAAa;EACb,gBAAgB;EAChB,kBAAkB;EAClB,eAAe;AACjB;AACA;EACE,mBAAmB;AACrB;;;;;AC5FA;EACE,aAAa;EACb,mBAAmB;EACnB,SAAS;EACT,uBAAuB;EACvB,kBAAkB;AACpB;AACA;EACE,aAAa;EACb,SAAS;AACX;AACA;EACE,+BAA+B;EAC/B,kBAAkB;EAClB,eAAe;EACf,gBAAgB;EAChB,aAAa;EACb,sBAAsB;EACtB,mBAAmB;EACnB,uBAAuB;EACvB,sCAAsC;EACtC,2BAA2B;EAC3B,0BAA0B;AAC5B;AACA;EACE,iBAAiB;EACjB,iBAAiB;EACjB,cAAc;EACd,kBAAkB;AACpB;AACA;EACE,iBAAiB;EACjB,WAAW;EACX,gBAAgB;AAClB;;;;;AClCA;EACE,gBAAgB;EAChB,mBAAmB;EACnB,uCAAuC;EACvC,mBAAmB;EACnB,aAAa;EACb,sBAAsB;EACtB,SAAS;EACT,oBAAoB;EACpB,iBAAiB;EACjB,2BAA2B;EAC3B,gBAAgB;EAChB,yBAAyB;EACzB,cAAc;AAChB;AACA;EACE,uCAAuC;EACvC,qBAAqB;AACvB;AACA;EACE,aAAa;EACb,mBAAmB;EACnB,8BAA8B;EAC9B,2BAA2B;EAC3B,SAAS;EACT,cAAc;AAChB;AACA;EACE,iBAAiB;EACjB,iBAAiB;EACjB,cAAc;EACd,gBAAgB;EAChB,SAAS;EACT,cAAc;EACd,mBAAmB;EACnB,gBAAgB;EAChB,uBAAuB;AACzB;AACA;EACE,mBAAmB;EACnB,WAAW;EACX,iBAAiB;EACjB,gBAAgB;EAChB,iBAAiB;EACjB,kBAAkB;EAClB,qBAAqB;EACrB,sBAAsB;EACtB,cAAc;EACd,mBAAmB;EACnB,gBAAgB;EAChB,uBAAuB;AACzB;AACA;EACE,iBAAiB;EACjB,WAAW;EACX,uBAAuB;EACvB,gBAAgB;AAClB;AACA;EACE,4BAA4B;EAC5B,gBAAgB;EAChB,iBAAiB;AACnB;AACA;EACE,iBAAiB;EACjB,iBAAiB;EACjB,oBAAiB;KAAjB,iBAAiB;EACjB,WAAW;AACb;AACA;EACE,uBAAuB;AACzB;AACA;EACE,YAAY;EACZ,gBAAgB;EAChB,mBAAmB;EACnB,WAAW;EACX,YAAY;EACZ,kBAAkB;EAClB,cAAc;EACd,cAAc;EACd,gBAAgB;EAChB,eAAe;EACf,2BAA2B;EAC3B,WAAW;AACb;AACA;EACE,mBAAmB;AACrB;;AAEA;EACE,kBAAkB;AACpB;AACA;EACE,kBAAkB;EAClB,SAAS;EACT,WAAW;EACX,aAAa;EACb,oBAAoB;EACpB,kBAAkB;EAClB,UAAU;EACV,gBAAgB;EAChB,YAAY;EACZ,gBAAgB;AAClB;AACA;EACE,aAAa;EACb,mBAAmB;EACnB,uBAAuB;EACvB,eAAe;AACjB;AACA,0BAA0B,mBAAmB,EAAE;AAC/C,uBAAuB,mBAAmB,EAAE;AAC5C,sBAAsB,mBAAmB,EAAE;AAC3C;EACE,WAAW;EACX,YAAY;EACZ,aAAa;EACb,mBAAmB;EACnB,uBAAuB;AACzB;AACA;EACE,WAAW;EACX,YAAY;EACZ,cAAc;EACd,WAAW;EACX,UAAU;AACZ;AACA;EACE,mBAAmB;EACnB,WAAW;EACX,iBAAiB;EACjB,gBAAgB;EAChB,aAAa;EACb,mBAAmB;EACnB,sBAAsB;EACtB,sBAAsB;AACxB;AACA;EACE,aAAa;EACb,8BAA8B;EAC9B,aAAa;EACb,wBAAwB;AAC1B;AACA;EACE,aAAa;EACb,mBAAmB;EACnB,iBAAiB;EACjB,WAAW;EACX,mBAAmB;EACnB,kBAAkB;EAClB,yBAAyB;EACzB,QAAQ;EACR,YAAY;EACZ,sBAAsB;AACxB;AACA;EACE,aAAa;EACb,mBAAmB;EACnB,gBAAgB;AAClB", "sources": ["webpack://amlak-auctions-block/./src/amlak-auctions-block/ImgSlider.css", "webpack://amlak-auctions-block/./src/amlak-auctions-block/Countdown.css", "webpack://amlak-auctions-block/./src/amlak-auctions-block/Card.css"], "sourcesContent": [".amlak-img-slider {\n  position: relative;\n  text-align: center;\n  overflow: visible;\n}\n.slider-img-fade-wrapper {\n  position: relative;\n  width: 100%;\n  min-height: 180px;\n  max-height: 200px;\n  z-index: 1;\n}\n.slider-img-fade {\n  position: absolute;\n  top: 0;\n  left: 0;\n  width: 100%;\n  height: 100%;\n  object-fit: cover;\n  opacity: 0;\n  transition: opacity 0.7s cubic-bezier(.4,0,.2,1);\n  border-radius: 8px;\n  z-index: 1;\n  pointer-events: none;\n}\n.slider-img-fade.active {\n  opacity: 1;\n  z-index: 2;\n  pointer-events: auto;\n}\n.slider-dots {\n  position: absolute;\n  left: 50%;\n  bottom: 10px;\n  transform: translateX(-50%);\n  z-index: 10;\n  display: flex;\n  gap: 6px;\n}\n.slider-img-fade-wrapper {\n  position: relative;\n  width: 100%;\n  min-height: 180px;\n  max-height: 200px;\n}\n.slider-img-fade {\n  position: absolute;\n  top: 0;\n  left: 0;\n  width: 100%;\n  height: 100%;\n  object-fit: cover;\n  opacity: 0;\n  transition: opacity 0.7s cubic-bezier(.4,0,.2,1);\n  border-radius: 8px;\n  z-index: 1;\n  pointer-events: none;\n}\n.slider-img-fade.active {\n  opacity: 1;\n  z-index: 2;\n  pointer-events: auto;\n}\n.slider-btn {\n  position: absolute;\n  top: 50%;\n  transform: translateY(-50%);\n  background: #fff;\n  border: none;\n  font-size: 1.5em;\n  cursor: pointer;\n  z-index: 2;\n  padding: 0 10px;\n  border-radius: 50%;\n  box-shadow: 0 2px 6px rgba(0,0,0,0.1);\n}\n.slider-btn.prev { left: 10px; }\n.slider-btn.next { right: 10px; }\n.slider-dots {\n  margin-top: 8px;\n}\n.dot {\n  display: inline-block;\n  width: 10px;\n  height: 10px;\n  margin: 0 3px;\n  background: #ccc;\n  border-radius: 50%;\n  cursor: pointer;\n}\n.dot.active {\n  background: #007cba;\n}\n", ".amlak-countdown-cards-row {\n  display: flex;\n  align-items: center;\n  gap: 10px;\n  justify-content: center;\n  margin: 14px 0 0 0;\n}\n.countdown-rects {\n  display: flex;\n  gap: 10px;\n}\n.countdown-rect {\n  background: rgba(30,30,30,0.08);\n  border-radius: 8px;\n  min-width: 48px;\n  min-height: 48px;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  box-shadow: 0 1px 4px rgba(0,0,0,0.08);\n  border: 1.5px solid #e0e0e0;\n  padding: 6px 10px 4px 10px;\n}\n.countdown-num {\n  font-size: 1.25em;\n  font-weight: bold;\n  color: #007cba;\n  margin-bottom: 2px;\n}\n.countdown-label {\n  font-size: 0.85em;\n  color: #666;\n  font-weight: 500;\n}\n", ".amlak-auction-card {\n  background: #fff;\n  border-radius: 16px;\n  box-shadow: 0 4px 24px rgba(0,0,0,0.10);\n  padding: 0 0 18px 0;\n  display: flex;\n  flex-direction: column;\n  gap: 10px;\n  align-items: stretch;\n  min-height: 370px;\n  transition: box-shadow 0.2s;\n  overflow: hidden;\n  border: 1px solid #f0f0f0;\n  direction: rtl;\n}\n.amlak-auction-card:hover {\n  box-shadow: 0 8px 32px rgba(0,0,0,0.16);\n  border-color: #e0e0e0;\n}\n.auction-title-agent-row {\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  padding: 14px 18px 8px 18px;\n  margin: 0;\n  direction: rtl;\n}\n.auction-title-new {\n  font-size: 1.18em;\n  font-weight: bold;\n  color: #1a2233;\n  line-height: 1.3;\n  margin: 0;\n  max-width: 60%;\n  white-space: nowrap;\n  overflow: hidden;\n  text-overflow: ellipsis;\n}\n.auction-agent-new {\n  background: #BB1919;\n  color: #fff;\n  font-size: 0.98em;\n  font-weight: 600;\n  padding: 2px 12px;\n  border-radius: 6px;\n  display: inline-block;\n  letter-spacing: 0.01em;\n  max-width: 35%;\n  white-space: nowrap;\n  overflow: hidden;\n  text-overflow: ellipsis;\n}\n.auction-type, .auction-location, .auction-assets-count, .auction-date-time, .auction-agent {\n  font-size: 0.97em;\n  color: #555;\n  margin: 0 16px 2px 16px;\n  line-height: 1.7;\n}\n.amlak-img-slider {\n  border-radius: 16px 16px 0 0;\n  overflow: hidden;\n  min-height: 180px;\n}\n.amlak-auction-card .slider-img {\n  min-height: 180px;\n  max-height: 200px;\n  object-fit: cover;\n  width: 100%;\n}\n.amlak-countdown {\n  margin: 8px 16px 0 16px;\n}\n.amlak-auction-card .auction-btn {\n  margin: 16px;\n  margin-top: 10px;\n  background: #007cba;\n  color: #fff;\n  border: none;\n  border-radius: 6px;\n  padding: 8px 0;\n  font-size: 1em;\n  font-weight: 500;\n  cursor: pointer;\n  transition: background 0.2s;\n  width: 100%;\n}\n.amlak-auction-card .auction-btn:hover {\n  background: #005fa3;\n}\n\n.auction-img-wrapper {\n  position: relative;\n}\n.auction-type-badge {\n  position: absolute;\n  top: 10px;\n  right: 10px;\n  display: flex;\n  align-items: stretch;\n  border-radius: 8px;\n  z-index: 2;\n  box-shadow: none;\n  border: none;\n  overflow: hidden;\n}\n.badge-icon-bg {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  padding: 0 12px;\n}\n.badge-icon-bg-إلكتروني { background: #2196f3; }\n.badge-icon-bg-حضوري { background: #43a047; }\n.badge-icon-bg-هجين { background: #8e44ad; }\n.badge-icon-large {\n  width: 28px;\n  height: 28px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n.badge-icon-large svg {\n  width: 22px;\n  height: 22px;\n  display: block;\n  color: #fff;\n  fill: #fff;\n}\n.badge-text-bg {\n  background: #f7f7f7;\n  color: #222;\n  font-size: 1.08em;\n  font-weight: 600;\n  display: flex;\n  align-items: center;\n  padding: 0 16px 0 12px;\n  letter-spacing: 0.01em;\n}\n.auction-info-grid {\n  display: grid;\n  grid-template-columns: 1fr 1fr;\n  gap: 8px 10px;\n  margin: 10px 16px 0 16px;\n}\n.auction-info-item {\n  display: flex;\n  align-items: center;\n  font-size: 0.97em;\n  color: #444;\n  background: #f8fafd;\n  border-radius: 6px;\n  padding: 6px 10px 6px 6px;\n  gap: 6px;\n  min-width: 0;\n  word-break: break-word;\n}\n.auction-info-item .icon {\n  display: flex;\n  align-items: center;\n  margin-left: 4px;\n}\n"], "names": [], "sourceRoot": ""}