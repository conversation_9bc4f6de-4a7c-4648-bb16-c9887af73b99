{"version": 3, "file": "amlak-auctions-block/index.js", "mappings": ";;;;;;;;;;;AAAA;;;;;;;;;;;;;;;;;;;;;;ACA0B;;AAE1B;AACA,MAAMC,qBAAqB,GAAGA,CAACC,IAAI,EAAEC,IAAI,KAAK;EAC1C,IAAI,CAACD,IAAI,EAAE,OAAO,EAAE;EACpB;EACA,MAAME,MAAM,GAAG,CACX,OAAO,EAAE,QAAQ,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,OAAO,EACnD,OAAO,EAAE,OAAO,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,CAC3D;EACD,MAAMC,WAAW,GAAG,CAChB,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EACxC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAC3C;EACD,MAAM,CAACC,IAAI,EAAEC,KAAK,EAAEC,GAAG,CAAC,GAAGN,IAAI,CAACO,KAAK,CAAC,GAAG,CAAC;EAC1C,IAAIC,SAAS,GAAGN,MAAM,CAACO,QAAQ,CAACJ,KAAK,EAAE,EAAE,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE;EACrD,IAAIK,MAAM,GAAG,GAAGD,QAAQ,CAACH,GAAG,EAAE,EAAE,CAAC,IAAIE,SAAS,IAAIJ,IAAI,GAAGH,IAAI,GAAG,KAAK,GAAGA,IAAI,GAAG,EAAE,EAAE;EACnF;EACA,IAAIS,MAAM,CAACC,MAAM,GAAG,EAAE,EAAE;IACpBH,SAAS,GAAGL,WAAW,CAACM,QAAQ,CAACJ,KAAK,EAAE,EAAE,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE;IACtDK,MAAM,GAAG,GAAGD,QAAQ,CAACH,GAAG,EAAE,EAAE,CAAC,IAAIE,SAAS,IAAIJ,IAAI,GAAGH,IAAI,GAAG,KAAK,GAAGA,IAAI,GAAG,EAAE,EAAE;EACnF;EACA,OAAOS,MAAM;AACjB,CAAC;AACmC;AACA;AAChB;AAAA;AAEpB,MAAMQ,IAAI,GAAGA,CAAC;EAAEC,OAAO;EAAEC;AAAS,CAAC,KAAK;EACpC;EACA;EACA,MAAMC,eAAe,GAAG;IACpB,UAAU,eACNJ,uDAAA;MAAKK,KAAK,EAAC,IAAI;MAACC,MAAM,EAAC,IAAI;MAACC,OAAO,EAAC,WAAW;MAACC,IAAI,EAAC,OAAO;MAAAC,QAAA,gBAACX,sDAAA;QAAMY,CAAC,EAAC,GAAG;QAACC,CAAC,EAAC,GAAG;QAACN,KAAK,EAAC,IAAI;QAACC,MAAM,EAAC,GAAG;QAACM,EAAE,EAAC;MAAG,CAAC,CAAC,eAAAd,sDAAA;QAAMY,CAAC,EAAC,GAAG;QAACC,CAAC,EAAC,IAAI;QAACN,KAAK,EAAC,IAAI;QAACC,MAAM,EAAC,GAAG;QAACM,EAAE,EAAC;MAAG,CAAC,CAAC;IAAA,CAAK,CACtK;IACD,OAAO,eACHd,sDAAA;MAAKO,KAAK,EAAC,IAAI;MAACC,MAAM,EAAC,IAAI;MAACC,OAAO,EAAC,WAAW;MAACC,IAAI,EAAC,OAAO;MAAAC,QAAA,eAACX,sDAAA;QAAMe,CAAC,EAAC;MAAsR,CAAC;IAAC,CAAK,CACrW;IACD,MAAM,eACFb,uDAAA;MAAMc,KAAK,EAAE;QAACC,OAAO,EAAC,MAAM;QAACC,UAAU,EAAC,QAAQ;QAACC,GAAG,EAAC;MAAK,CAAE;MAAAR,QAAA,gBACxDT,uDAAA;QAAKK,KAAK,EAAC,IAAI;QAACC,MAAM,EAAC,IAAI;QAACC,OAAO,EAAC,WAAW;QAACC,IAAI,EAAC,OAAO;QAAAC,QAAA,gBAACX,sDAAA;UAAMY,CAAC,EAAC,GAAG;UAACC,CAAC,EAAC,GAAG;UAACN,KAAK,EAAC,IAAI;UAACC,MAAM,EAAC,GAAG;UAACM,EAAE,EAAC;QAAG,CAAC,CAAC,eAAAd,sDAAA;UAAMY,CAAC,EAAC,GAAG;UAACC,CAAC,EAAC,IAAI;UAACN,KAAK,EAAC,IAAI;UAACC,MAAM,EAAC,GAAG;UAACM,EAAE,EAAC;QAAG,CAAC,CAAC;MAAA,CAAK,CAAC,eACpKd,sDAAA;QAAKO,KAAK,EAAC,IAAI;QAACC,MAAM,EAAC,IAAI;QAACC,OAAO,EAAC,WAAW;QAACC,IAAI,EAAC,OAAO;QAAAC,QAAA,eAACX,sDAAA;UAAMe,CAAC,EAAC;QAAsR,CAAC;MAAC,CAAK,CAAC;IAAA,CACjW;EAEd,CAAC;EAED,oBACIb,uDAAA;IAAKkB,SAAS,EAAC,oBAAoB;IAAAT,QAAA,GAC9BN,QAAQ,CAACgB,UAAU,IAAIjB,OAAO,CAACkB,MAAM,iBAClCpB,uDAAA;MAAKkB,SAAS,EAAC,qBAAqB;MAAAT,QAAA,GAC/BN,QAAQ,CAACkB,QAAQ,iBACdrB,uDAAA;QAAMkB,SAAS,EAAE,mCAAmChB,OAAO,CAACoB,IAAI,EAAG;QAAAb,QAAA,gBAC/DX,sDAAA;UAAMoB,SAAS,EAAE,+BAA+BhB,OAAO,CAACoB,IAAI,EAAG;UAAAb,QAAA,eAC3DX,sDAAA;YAAMoB,SAAS,EAAC,kBAAkB;YAAAT,QAAA,EAAEL,eAAe,CAACF,OAAO,CAACoB,IAAI;UAAC,CAAO;QAAC,CACvE,CAAC,eACPxB,sDAAA;UAAMoB,SAAS,EAAC,eAAe;UAAAT,QAAA,EAAEP,OAAO,CAACoB;QAAI,CAAO,CAAC;MAAA,CACnD,CACT,eACDxB,sDAAA,CAACH,kDAAS;QAACyB,MAAM,EAAElB,OAAO,CAACkB;MAAO,CAAE,CAAC;IAAA,CACpC,CACR,EACAjB,QAAQ,CAACoB,SAAS,iBACfvB,uDAAA;MAAKkB,SAAS,EAAC,yBAAyB;MAAAT,QAAA,gBACpCX,sDAAA;QAAIoB,SAAS,EAAC,mBAAmB;QAAAT,QAAA,EAAEP,OAAO,CAACsB;MAAK,CAAK,CAAC,EACrDrB,QAAQ,CAACsB,SAAS,iBACf3B,sDAAA;QAAKoB,SAAS,EAAC,mBAAmB;QAAAT,QAAA,EAAEP,OAAO,CAACwB;MAAK,CAAM,CAC1D;IAAA,CACA,CACR,eACD1B,uDAAA;MAAKkB,SAAS,EAAC,mBAAmB;MAAAT,QAAA,GAC7BN,QAAQ,CAACwB,YAAY,iBAClB3B,uDAAA;QAAKkB,SAAS,EAAC,mBAAmB;QAAAT,QAAA,gBAC9BX,sDAAA;UAAMoB,SAAS,EAAC,MAAM;UAAAT,QAAA,eAClBX,sDAAA;YAAKO,KAAK,EAAC,IAAI;YAACC,MAAM,EAAC,IAAI;YAACE,IAAI,EAAC,SAAS;YAACD,OAAO,EAAC,WAAW;YAAAE,QAAA,eAACX,sDAAA;cAAMe,CAAC,EAAC;YAAoH,CAAC;UAAC,CAAK;QAAC,CACjM,CAAC,EACNX,OAAO,CAAC0B,QAAQ;MAAA,CAChB,CACR,EACAzB,QAAQ,CAAC0B,eAAe,iBACrB7B,uDAAA;QAAKkB,SAAS,EAAC,mBAAmB;QAAAT,QAAA,gBAC9BX,sDAAA;UAAMoB,SAAS,EAAC,MAAM;UAAAT,QAAA,eAElBX,sDAAA;YAAKO,KAAK,EAAC,IAAI;YAACC,MAAM,EAAC,IAAI;YAACE,IAAI,EAAC,SAAS;YAACD,OAAO,EAAC,WAAW;YAAAE,QAAA,eAACX,sDAAA;cAAMe,CAAC,EAAC;YAAoD,CAAC;UAAC,CAAK;QAAC,CACjI,CAAC,EACNX,OAAO,CAAC4B,WAAW,EAAC,GAAC,EAAC5B,OAAO,CAAC4B,WAAW,KAAK,CAAC,GAAG,KAAK,GAAG,MAAM;MAAA,CAChE,CACR,EACA3B,QAAQ,CAAC4B,YAAY,iBAClB/B,uDAAA;QAAKkB,SAAS,EAAC,kBAAkB;QAAAT,QAAA,gBAC7BX,sDAAA;UAAMoB,SAAS,EAAC,MAAM;UAAAT,QAAA,eAElBX,sDAAA;YAAKO,KAAK,EAAC,IAAI;YAACC,MAAM,EAAC,IAAI;YAACE,IAAI,EAAC,SAAS;YAACD,OAAO,EAAC,WAAW;YAAAE,QAAA,eAACX,sDAAA;cAAMe,CAAC,EAAC;YAA4I,CAAC;UAAC,CAAK;QAAC,CACzN,CAAC,eACPf,sDAAA;UAAMoB,SAAS,EAAC,mBAAmB;UAAAT,QAAA,EAAE3B,qBAAqB,CAACoB,OAAO,CAACnB,IAAI,EAAEmB,OAAO,CAAClB,IAAI;QAAC,CAAO,CAAC;MAAA,CAC7F,CACR;IAAA,CACA,CAAC,EACLmB,QAAQ,CAAC6B,aAAa,IAAI9B,OAAO,CAAC+B,WAAW,iBAC1CnC,sDAAA,CAACF,kDAAS;MAACqC,WAAW,EAAE/B,OAAO,CAAC+B;IAAY,CAAE,CACjD;EAAA,CACA,CAAC;AAEd,CAAC;AAED,iEAAehC,IAAI;;;;;;;;;;;ACxGnB;;;;;;;;;;;;;;;;;;;;ACAmD;AAC1B;AAAA;AAEzB,MAAMmC,WAAW,GAAIH,WAAW,IAAK;EACjC,MAAMI,KAAK,GAAGC,IAAI,CAACC,KAAK,CAACN,WAAW,CAAC,GAAGK,IAAI,CAACE,GAAG,CAAC,CAAC;EAClD,MAAMC,OAAO,GAAGC,IAAI,CAACC,KAAK,CAAEN,KAAK,GAAG,IAAI,GAAI,EAAE,CAAC;EAC/C,MAAMO,OAAO,GAAGF,IAAI,CAACC,KAAK,CAAEN,KAAK,GAAG,IAAI,GAAG,EAAE,GAAI,EAAE,CAAC;EACpD,MAAMQ,KAAK,GAAGH,IAAI,CAACC,KAAK,CAAEN,KAAK,IAAI,IAAI,GAAG,EAAE,GAAG,EAAE,CAAC,GAAI,EAAE,CAAC;EACzD,MAAMS,IAAI,GAAGJ,IAAI,CAACC,KAAK,CAACN,KAAK,IAAI,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;EACtD,OAAO;IAAEA,KAAK;IAAES,IAAI;IAAED,KAAK;IAAED,OAAO;IAAEH;EAAQ,CAAC;AACnD,CAAC;AAED,MAAM7C,SAAS,GAAGA,CAAC;EAAEqC;AAAY,CAAC,KAAK;EACnC,MAAM,CAACc,QAAQ,EAAEC,WAAW,CAAC,GAAGb,+CAAQ,CAACC,WAAW,CAACH,WAAW,CAAC,CAAC;EAElEC,gDAAS,CAAC,MAAM;IACZ,MAAMe,KAAK,GAAGC,WAAW,CAAC,MAAM;MAC5BF,WAAW,CAACZ,WAAW,CAACH,WAAW,CAAC,CAAC;IACzC,CAAC,EAAE,IAAI,CAAC;IACR,OAAO,MAAMkB,aAAa,CAACF,KAAK,CAAC;EACrC,CAAC,EAAE,CAAChB,WAAW,CAAC,CAAC;EAEjB,IAAIc,QAAQ,CAACV,KAAK,IAAI,CAAC,EAAE;IACrB,oBAAOvC,sDAAA;MAAKoB,SAAS,EAAC,iBAAiB;MAAAT,QAAA,EAAC;IAAY,CAAK,CAAC;EAC9D;;EAEA;EACA,oBACIX,sDAAA;IAAKoB,SAAS,EAAC,2BAA2B;IAAAT,QAAA,eACtCT,uDAAA;MAAKkB,SAAS,EAAC,iBAAiB;MAAAT,QAAA,gBAC5BT,uDAAA;QAAKkB,SAAS,EAAC,gBAAgB;QAAAT,QAAA,gBAACX,sDAAA;UAAKoB,SAAS,EAAC,eAAe;UAAAT,QAAA,EAAEsC,QAAQ,CAACD;QAAI,CAAM,CAAC,eAAAhD,sDAAA;UAAKoB,SAAS,EAAC,iBAAiB;UAAAT,QAAA,EAAC;QAAG,CAAK,CAAC;MAAA,CAAK,CAAC,eACpIT,uDAAA;QAAKkB,SAAS,EAAC,gBAAgB;QAAAT,QAAA,gBAACX,sDAAA;UAAKoB,SAAS,EAAC,eAAe;UAAAT,QAAA,EAAEsC,QAAQ,CAACF;QAAK,CAAM,CAAC,eAAA/C,sDAAA;UAAKoB,SAAS,EAAC,iBAAiB;UAAAT,QAAA,EAAC;QAAI,CAAK,CAAC;MAAA,CAAK,CAAC,eACtIT,uDAAA;QAAKkB,SAAS,EAAC,gBAAgB;QAAAT,QAAA,gBAACX,sDAAA;UAAKoB,SAAS,EAAC,eAAe;UAAAT,QAAA,EAAEsC,QAAQ,CAACH;QAAO,CAAM,CAAC,eAAA9C,sDAAA;UAAKoB,SAAS,EAAC,iBAAiB;UAAAT,QAAA,EAAC;QAAK,CAAK,CAAC;MAAA,CAAK,CAAC,eACzIT,uDAAA;QAAKkB,SAAS,EAAC,gBAAgB;QAAAT,QAAA,gBAACX,sDAAA;UAAKoB,SAAS,EAAC,eAAe;UAAAT,QAAA,EAAEsC,QAAQ,CAACN;QAAO,CAAM,CAAC,eAAA3C,sDAAA;UAAKoB,SAAS,EAAC,iBAAiB;UAAAT,QAAA,EAAC;QAAK,CAAK,CAAC;MAAA,CAAK,CAAC;IAAA,CACxI;EAAC,CACL,CAAC;AAEd,CAAC;AAED,iEAAeb,SAAS;;;;;;;;;;;ACvCxB;;;;;;;;;;;;;;;;;;;;ACA2D;AAClC;AAAA;AAEzB,MAAMyD,mBAAmB,GAAG,IAAI,CAAC,CAAC;;AAElC,MAAM1D,SAAS,GAAGA,CAAC;EAAEyB,MAAM,GAAG;AAAG,CAAC,KAAK;EACnC,MAAM,CAACkC,OAAO,EAAEC,UAAU,CAAC,GAAGpB,+CAAQ,CAAC,CAAC,CAAC;EACzC,MAAM,CAACqB,MAAM,EAAEC,SAAS,CAAC,GAAGtB,+CAAQ,CAAC,KAAK,CAAC;EAC3C,MAAMuB,WAAW,GAAGN,6CAAM,CAAC,CAAC;EAC5B,IAAI,CAAChC,MAAM,CAAC1B,MAAM,EAAE,OAAO,IAAI;;EAE/B;EACAwC,gDAAS,CAAC,MAAM;IACZ,IAAIsB,MAAM,EAAE;IACZE,WAAW,CAACJ,OAAO,GAAGJ,WAAW,CAAC,MAAM;MACpCK,UAAU,CAAEI,IAAI,IAAK,CAACA,IAAI,GAAG,CAAC,IAAIvC,MAAM,CAAC1B,MAAM,CAAC;IACpD,CAAC,EAAE2D,mBAAmB,CAAC;IACvB,OAAO,MAAMF,aAAa,CAACO,WAAW,CAACJ,OAAO,CAAC;EACnD,CAAC,EAAE,CAAClC,MAAM,CAAC1B,MAAM,EAAE8D,MAAM,CAAC,CAAC;;EAE3B;EACA,MAAMI,gBAAgB,GAAGA,CAAA,KAAMH,SAAS,CAAC,IAAI,CAAC;EAC9C,MAAMI,gBAAgB,GAAGA,CAAA,KAAMJ,SAAS,CAAC,KAAK,CAAC;EAE/C,oBACIzD,uDAAA;IAAKkB,SAAS,EAAC,kBAAkB;IAAC4C,YAAY,EAAEF,gBAAiB;IAACG,YAAY,EAAEF,gBAAiB;IAAApD,QAAA,gBAC7FX,sDAAA;MAAKoB,SAAS,EAAC,yBAAyB;MAAAT,QAAA,EACnCW,MAAM,CAAC4C,GAAG,CAAC,CAACC,GAAG,EAAEC,GAAG,kBACjBpE,sDAAA;QAEIqE,GAAG,EAAEF,GAAI;QACTG,GAAG,EAAC,+DAAa;QACjBlD,SAAS,EACL,iBAAiB,IAAIgD,GAAG,KAAKZ,OAAO,GAAG,SAAS,GAAG,EAAE,CACxD;QACDxC,KAAK,EAAE;UAAEuD,MAAM,EAAEH,GAAG,KAAKZ,OAAO,GAAG,CAAC,GAAG;QAAE;MAAE,GANtCY,GAOR,CACJ;IAAC,CACD,CAAC,eACNpE,sDAAA;MAAKoB,SAAS,EAAC,aAAa;MAAAT,QAAA,EACvBW,MAAM,CAAC4C,GAAG,CAAC,CAACM,CAAC,EAAEJ,GAAG,kBACfpE,sDAAA;QAEIoB,SAAS,EAAEgD,GAAG,KAAKZ,OAAO,GAAG,YAAY,GAAG,KAAM;QAClDiB,OAAO,EAAEA,CAAA,KAAMhB,UAAU,CAACW,GAAG;MAAE,GAF1BA,GAGR,CACJ;IAAC,CACD,CAAC;EAAA,CACL,CAAC;AAEd,CAAC;AAED,iEAAevE,SAAS;;;;;;;;;;;;;;;;;;;;ACpDE;AACqD;AAAA;AAE/E,MAAMgF,aAAa,GAAGA,CAAC;EAAEC,UAAU;EAAEC;AAAc,CAAC,KAAK;EACrD,oBACI7E,uDAAA,CAACwE,4DAAS;IAAChD,KAAK,EAAC,gHAAsB;IAACsD,WAAW,EAAE,IAAK;IAAArE,QAAA,gBACtDX,sDAAA,CAAC4E,+DAAY;MACTK,KAAK,EAAC,sGAAsB;MAC5BC,KAAK,EAAEJ,UAAU,CAACK,OAAQ;MAC1BC,QAAQ,EAAGC,GAAG,IAAKN,aAAa,CAAC;QAAEI,OAAO,EAAEE;MAAI,CAAC,CAAE;MACnDC,GAAG,EAAE,CAAE;MACPC,GAAG,EAAE;IAAE,CACV,CAAC,eACFvF,sDAAA,CAAC4E,+DAAY;MACTK,KAAK,EAAC,sHAAuB;MAC7BC,KAAK,EAAEJ,UAAU,CAACU,aAAc;MAChCJ,QAAQ,EAAGC,GAAG,IAAKN,aAAa,CAAC;QAAES,aAAa,EAAEH;MAAI,CAAC,CAAE;MACzDC,GAAG,EAAE,CAAE;MACPC,GAAG,EAAE;IAAG,CACX,CAAC,eACFvF,sDAAA,CAAC2E,gEAAa;MACVM,KAAK,EAAC,+DAAa;MACnBQ,OAAO,EAAEX,UAAU,CAACzD,UAAW;MAC/B+D,QAAQ,EAAGC,GAAG,IAAKN,aAAa,CAAC;QAAE1D,UAAU,EAAEgE;MAAI,CAAC;IAAE,CACzD,CAAC,eACFrF,sDAAA,CAAC2E,gEAAa;MACVM,KAAK,EAAC,wFAAkB;MACxBQ,OAAO,EAAEX,UAAU,CAACrD,SAAU;MAC9B2D,QAAQ,EAAGC,GAAG,IAAKN,aAAa,CAAC;QAAEtD,SAAS,EAAE4D;MAAI,CAAC;IAAE,CACxD,CAAC,eACFrF,sDAAA,CAAC2E,gEAAa;MACVM,KAAK,EAAC,wFAAkB;MACxBQ,OAAO,EAAEX,UAAU,CAACvD,QAAS;MAC7B6D,QAAQ,EAAGC,GAAG,IAAKN,aAAa,CAAC;QAAExD,QAAQ,EAAE8D;MAAI,CAAC;IAAE,CACvD,CAAC,eACFrF,sDAAA,CAAC2E,gEAAa;MACVM,KAAK,EAAC,qEAAc;MACpBQ,OAAO,EAAEX,UAAU,CAACjD,YAAa;MACjCuD,QAAQ,EAAGC,GAAG,IAAKN,aAAa,CAAC;QAAElD,YAAY,EAAEwD;MAAI,CAAC;IAAE,CAC3D,CAAC,eACFrF,sDAAA,CAAC2E,gEAAa;MACVM,KAAK,EAAC,wFAAkB;MACxBQ,OAAO,EAAEX,UAAU,CAAC/C,eAAgB;MACpCqD,QAAQ,EAAGC,GAAG,IAAKN,aAAa,CAAC;QAAEhD,eAAe,EAAEsD;MAAI,CAAC;IAAE,CAC9D,CAAC,eACFrF,sDAAA,CAAC2E,gEAAa;MACVM,KAAK,EAAC,gHAAsB;MAC5BQ,OAAO,EAAEX,UAAU,CAAC7C,YAAa;MACjCmD,QAAQ,EAAGC,GAAG,IAAKN,aAAa,CAAC;QAAE9C,YAAY,EAAEoD;MAAI,CAAC;IAAE,CAC3D,CAAC,eACFrF,sDAAA,CAAC2E,gEAAa;MACVM,KAAK,EAAC,sHAAuB;MAC7BQ,OAAO,EAAEX,UAAU,CAAC5C,aAAc;MAClCkD,QAAQ,EAAGC,GAAG,IAAKN,aAAa,CAAC;QAAE7C,aAAa,EAAEmD;MAAI,CAAC;IAAE,CAC5D,CAAC,eACFrF,sDAAA,CAAC2E,gEAAa;MACVM,KAAK,EAAC,wFAAkB;MACxBQ,OAAO,EAAEX,UAAU,CAACnD,SAAU;MAC9ByD,QAAQ,EAAGC,GAAG,IAAKN,aAAa,CAAC;QAAEpD,SAAS,EAAE0D;MAAI,CAAC;IAAE,CACxD,CAAC;EAAA,CACK,CAAC;AAEpB,CAAC;AAED,iEAAeR,aAAa;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AChEuB;AACwB;AACjD;AACkB;AACtB;AAAA;AAEtB,MAAMe,YAAY,GAAG,CACjB;EACIC,EAAE,EAAE,CAAC;EACLnE,KAAK,EAAE,sBAAsB;EAC7BF,IAAI,EAAE,UAAU;EAChBM,QAAQ,EAAE,iBAAiB;EAC3BE,WAAW,EAAE,CAAC;EACd/C,IAAI,EAAE,YAAY;EAClBC,IAAI,EAAE,OAAO;EACbiD,WAAW,EAAE,qBAAqB;EAClCP,KAAK,EAAE,aAAa;EACpBN,MAAM,EAAE,CACJ,8FAA8F,EAC9F,8FAA8F;AAEtG,CAAC,EACD;EACIuE,EAAE,EAAE,CAAC;EACLnE,KAAK,EAAE,kBAAkB;EACzBF,IAAI,EAAE,OAAO;EACbM,QAAQ,EAAE,eAAe;EACzBE,WAAW,EAAE,CAAC;EACd/C,IAAI,EAAE,YAAY;EAClBC,IAAI,EAAE,OAAO;EACbiD,WAAW,EAAE,qBAAqB;EAClCP,KAAK,EAAE,WAAW;EAClBN,MAAM,EAAE,CACJ,8FAA8F,EAC9F,8FAA8F;AAEtG,CAAC,EACD;EACIuE,EAAE,EAAE,CAAC;EACLnE,KAAK,EAAE,mBAAmB;EAC1BF,IAAI,EAAE,MAAM;EACZM,QAAQ,EAAE,iBAAiB;EAC3BE,WAAW,EAAE,CAAC;EACd/C,IAAI,EAAE,YAAY;EAClBC,IAAI,EAAE,OAAO;EACbiD,WAAW,EAAE,qBAAqB;EAClCP,KAAK,EAAE,eAAe;EACtBN,MAAM,EAAE,CACJ,8FAA8F,EAC9F,8FAA8F;AAEtG,CAAC,CACJ;AAED,MAAMwE,YAAY,GAAGA,CAAC;EAAEC,QAAQ;EAAEZ,OAAO;EAAE9E;AAAS,CAAC,kBACjDL,sDAAA;EACIoB,SAAS,EAAC,qBAAqB;EAC/BJ,KAAK,EAAE;IACHC,OAAO,EAAE,MAAM;IACf+E,mBAAmB,EAAE,UAAUb,OAAO,QAAQ;IAC9ChE,GAAG,EAAE;EACT,CAAE;EAAAR,QAAA,EAEDoF,QAAQ,CAAC7B,GAAG,CAAE9D,OAAO,iBAClBJ,sDAAA,CAACG,6CAAI;IAAkBC,OAAO,EAAEA,OAAQ;IAACC,QAAQ,EAAEA;EAAS,GAAjDD,OAAO,CAACyF,EAA2C,CACjE;AAAC,CACD,CACR;AAEc,SAASI,IAAIA,CAAC;EAAEnB,UAAU;EAAEC;AAAc,CAAC,EAAE;EACxD;EACA,MAAM,CAACgB,QAAQ,EAAEG,WAAW,CAAC,GAAG7D,+CAAQ,CAACuD,YAAY,CAACO,KAAK,CAAC,CAAC,EAAErB,UAAU,CAACU,aAAa,CAAC,CAAC;EAEzFpD,gDAAS,CAAC,MAAM;IACZ8D,WAAW,CAACN,YAAY,CAACO,KAAK,CAAC,CAAC,EAAErB,UAAU,CAACU,aAAa,CAAC,CAAC;EAChE,CAAC,EAAE,CAACV,UAAU,CAACU,aAAa,CAAC,CAAC;EAE9B,oBACItF,uDAAA;IAAA,GAASyF,sEAAa,CAAC,CAAC;IAAAhF,QAAA,gBACpBX,sDAAA,CAAC0F,sEAAiB;MAAA/E,QAAA,eACdX,sDAAA,CAAC6E,sDAAa;QAACC,UAAU,EAAEA,UAAW;QAACC,aAAa,EAAEA;MAAc,CAAE;IAAC,CACxD,CAAC,eACpB/E,sDAAA,CAAC8F,YAAY;MAACC,QAAQ,EAAEA,QAAS;MAACZ,OAAO,EAAEL,UAAU,CAACK,OAAQ;MAAC9E,QAAQ,EAAEyE;IAAW,CAAE,CAAC;EAAA,CACtF,CAAC;AAEd;;;;;;;;;;;;;;;;;ACrFA;AACA;AACA;AACA;AACA;AACsD;;AAEtD;AACA;AACA;AACA;AACA;AACA;AACA;AACsB;;AAEtB;AACA;AACA;AAC0B;AACA;AACU;;AAEpC;AACA;AACA;AACA;AACA;AACAsB,oEAAiB,CAAEE,6CAAa,EAAE;EACjC;AACD;AACA;EACCE,IAAI,EAAEP,6CAAI;EAEV;AACD;AACA;EACCI,IAAIA,+CAAAA;AACL,CAAE,CAAC;;;;;;;;;;;;;;;;;;ACtCH;AACA;AACA;AACA;AACA;AACA;AACwD;;AAExD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AARA;AASe,SAASA,IAAIA,CAAA,EAAG;EAC9B,oBACCrG,sDAAA;IAAA,GAAQ2F,kEAAa,CAACU,IAAI,CAAC,CAAC;IAAA1F,QAAA,EACzB;EAAsD,CACtD,CAAC;AAEN;;;;;;;;;;;ACvBA;;;;;;;;;;;ACAA;;;;;;;;;;ACAA;;;;;;;;;;ACAA;;;;;;;;;;ACAA;;;;;;;;;;ACAA;;;;;;UCAA;UACA;;UAEA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;;UAEA;UACA;;UAEA;UACA;UACA;;UAEA;UACA;;;;;WCzBA;WACA;WACA;WACA;WACA,+BAA+B,wCAAwC;WACvE;WACA;WACA;WACA;WACA,iBAAiB,qBAAqB;WACtC;WACA;WACA,kBAAkB,qBAAqB;WACvC;WACA;WACA,KAAK;WACL;WACA;WACA;WACA;WACA;WACA;WACA;WACA;WACA;WACA;WACA;WACA;;;;;WC3BA;WACA;WACA;WACA;WACA;WACA,iCAAiC,WAAW;WAC5C;WACA;;;;;WCPA;WACA;WACA;WACA;WACA,yCAAyC,wCAAwC;WACjF;WACA;WACA;;;;;WCPA;;;;;WCAA;WACA;WACA;WACA,uDAAuD,iBAAiB;WACxE;WACA,gDAAgD,aAAa;WAC7D;;;;;WCNA;;WAEA;WACA;WACA;WACA;WACA;WACA;WACA;;WAEA;;WAEA;;WAEA;;WAEA;;WAEA;;WAEA;;WAEA;WACA;WACA;WACA;WACA;WACA;WACA;WACA;WACA;WACA;WACA;WACA;WACA;WACA;WACA;WACA,MAAM,qBAAqB;WAC3B;WACA;WACA;WACA;WACA;WACA;WACA;WACA;;WAEA;WACA;WACA;;;;;UEjDA;UACA;UACA;UACA;UACA", "sources": ["webpack://amlak-auctions-block/./src/amlak-auctions-block/Card.css", "webpack://amlak-auctions-block/./src/amlak-auctions-block/Card.js", "webpack://amlak-auctions-block/./src/amlak-auctions-block/Countdown.css", "webpack://amlak-auctions-block/./src/amlak-auctions-block/Countdown.js", "webpack://amlak-auctions-block/./src/amlak-auctions-block/ImgSlider.css", "webpack://amlak-auctions-block/./src/amlak-auctions-block/ImgSlider.js", "webpack://amlak-auctions-block/./src/amlak-auctions-block/SettingsPanel.js", "webpack://amlak-auctions-block/./src/amlak-auctions-block/edit.js", "webpack://amlak-auctions-block/./src/amlak-auctions-block/index.js", "webpack://amlak-auctions-block/./src/amlak-auctions-block/save.js", "webpack://amlak-auctions-block/./src/amlak-auctions-block/style.scss", "webpack://amlak-auctions-block/external window [\"wp\",\"blockEditor\"]", "webpack://amlak-auctions-block/external window [\"wp\",\"blocks\"]", "webpack://amlak-auctions-block/external window [\"wp\",\"components\"]", "webpack://amlak-auctions-block/external window \"React\"", "webpack://amlak-auctions-block/external window \"ReactJSXRuntime\"", "webpack://amlak-auctions-block/webpack/bootstrap", "webpack://amlak-auctions-block/webpack/runtime/chunk loaded", "webpack://amlak-auctions-block/webpack/runtime/compat get default export", "webpack://amlak-auctions-block/webpack/runtime/define property getters", "webpack://amlak-auctions-block/webpack/runtime/hasOwnProperty shorthand", "webpack://amlak-auctions-block/webpack/runtime/make namespace object", "webpack://amlak-auctions-block/webpack/runtime/jsonp chunk loading", "webpack://amlak-auctions-block/webpack/before-startup", "webpack://amlak-auctions-block/webpack/startup", "webpack://amlak-auctions-block/webpack/after-startup"], "sourcesContent": ["// extracted by mini-css-extract-plugin\nexport {};", "import React from 'react';\n\n// دالة لتحويل التاريخ إلى نص عربي جميل\nconst formatAuctionDateTime = (date, time) => {\n    if (!date) return '';\n    // إذا كان النص طويل جداً اختصر اسم الشهر\n    const months = [\n        'يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو',\n        'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'\n    ];\n    const monthsShort = [\n        'ينا', 'فبر', 'مار', 'أبر', 'ماي', 'يون',\n        'يول', 'أغس', 'سبت', 'أكت', 'نوف', 'ديس'\n    ];\n    const [year, month, day] = date.split('-');\n    let monthName = months[parseInt(month, 10) - 1] || '';\n    let result = `${parseInt(day, 10)} ${monthName} ${year}${time ? ' - ' + time : ''}`;\n    // إذا كان النص طويل جداً اختصر الشهر\n    if (result.length > 22) {\n        monthName = monthsShort[parseInt(month, 10) - 1] || '';\n        result = `${parseInt(day, 10)} ${monthName} ${year}${time ? ' - ' + time : ''}`;\n    }\n    return result;\n};\nimport ImgSlider from './ImgSlider';\nimport Countdown from './Countdown';\nimport './Card.css';\n\nconst Card = ({ auction, settings }) => {\n    // أيقونة نوع المزاد حسب النوع\n    // تصميم أيقونة واضحة جدًا لكل نوع\n    const auctionTypeIcon = {\n        'إلكتروني': (\n            <svg width=\"22\" height=\"22\" viewBox=\"0 0 24 24\" fill=\"white\"><rect x=\"3\" y=\"6\" width=\"18\" height=\"9\" rx=\"2\"/><rect x=\"7\" y=\"16\" width=\"10\" height=\"2\" rx=\"1\"/></svg>\n        ),\n        'حضوري': (\n            <svg width=\"22\" height=\"22\" viewBox=\"0 0 24 24\" fill=\"white\"><path d=\"M16 11c1.66 0 2.99-1.34 2.99-3S17.66 5 16 5s-3 1.34-3 3 1.34 3 3 3zm-8 0c1.66 0 2.99-1.34 2.99-3S9.66 5 8 5 5 6.34 5 8s1.34 3 3 3zm0 2c-2.33 0-7 1.17-7 3.5V19h14v-2.5c0-2.33-4.67-3.5-7-3.5zm8 0c-.29 0-.62.02-.97.05C15.64 13.36 17 14.28 17 15.5V19h6v-2.5c0-2.33-4.67-3.5-7-3.5z\"/></svg>\n        ),\n        'هجين': (\n            <span style={{display:'flex',alignItems:'center',gap:'1px'}}>\n                <svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"white\"><rect x=\"3\" y=\"6\" width=\"18\" height=\"9\" rx=\"2\"/><rect x=\"7\" y=\"16\" width=\"10\" height=\"2\" rx=\"1\"/></svg>\n                <svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"white\"><path d=\"M16 11c1.66 0 2.99-1.34 2.99-3S17.66 5 16 5s-3 1.34-3 3 1.34 3 3 3zm-8 0c1.66 0 2.99-1.34 2.99-3S9.66 5 8 5 5 6.34 5 8s1.34 3 3 3zm0 2c-2.33 0-7 1.17-7 3.5V19h14v-2.5c0-2.33-4.67-3.5-7-3.5zm8 0c-.29 0-.62.02-.97.05C15.64 13.36 17 14.28 17 15.5V19h6v-2.5c0-2.33-4.67-3.5-7-3.5z\"/></svg>\n            </span>\n        ),\n    };\n\n    return (\n        <div className=\"amlak-auction-card\">\n            {settings.showImages && auction.images && (\n                <div className=\"auction-img-wrapper\">\n                    {settings.showType && (\n                        <span className={`auction-type-badge auction-type-${auction.type}`}>\n                            <span className={`badge-icon-bg badge-icon-bg-${auction.type}`}>\n                                <span className=\"badge-icon-large\">{auctionTypeIcon[auction.type]}</span>\n                            </span>\n                            <span className=\"badge-text-bg\">{auction.type}</span>\n                        </span>\n                    )}\n                    <ImgSlider images={auction.images} />\n                </div>\n            )}\n            {settings.showTitle && (\n                <div className=\"auction-title-agent-row\">\n                    <h2 className=\"auction-title-new\">{auction.title}</h2>\n                    {settings.showAgent && (\n                        <div className=\"auction-agent-new\">{auction.agent}</div>\n                    )}\n                </div>\n            )}\n            <div className=\"auction-info-grid\">\n                {settings.showLocation && (\n                    <div className=\"auction-info-item\">\n                        <span className=\"icon\">\n                            <svg width=\"24\" height=\"24\" fill=\"#BB1919\" viewBox=\"0 0 24 24\"><path d=\"M12 2C8.13 2 5 5.13 5 9c0 5.25 7 13 7 13s7-7.75 7-13c0-3.87-3.13-7-7-7zm0 11.5A2.5 2.5 0 1112 8a2.5 2.5 0 010 5.5z\"/></svg>\n                        </span>\n                        {auction.location}\n                    </div>\n                )}\n                {settings.showAssetsCount && (\n                    <div className=\"auction-info-item\">\n                        <span className=\"icon\">\n                            {/* أيقونة مبنى عالي */}\n                            <svg width=\"24\" height=\"24\" fill=\"#BB1919\" viewBox=\"0 0 24 24\"><path d=\"M4 22V2h16v20h-7v-6h-2v6H4zm2-2h4v-6h4v6h4V4H6v16z\"/></svg>\n                        </span>\n                        {auction.assetsCount} {auction.assetsCount === 1 ? 'أصل' : 'أصول'}\n                    </div>\n                )}\n                {settings.showDateTime && (\n                    <div className=\"auction-date-row\">\n                        <span className=\"icon\">\n                            {/* أيقونة تقويم */}\n                            <svg width=\"24\" height=\"24\" fill=\"#BB1919\" viewBox=\"0 0 24 24\"><path d=\"M19 4h-1V2h-2v2H8V2H6v2H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V6c0-1.1-.9-2-2-2zm0 16H5V9h14v11zm0-13H5V6h14v1zm-7 5h5v5h-5z\"/></svg>\n                        </span>\n                        <span className=\"auction-date-text\">{formatAuctionDateTime(auction.date, auction.time)}</span>\n                    </div>\n                )}\n            </div>\n            {settings.showCountdown && auction.endDateTime && (\n                <Countdown endDateTime={auction.endDateTime} />\n            )}\n        </div>\n    );\n};\n\nexport default Card;\n", "// extracted by mini-css-extract-plugin\nexport {};", "import React, { useEffect, useState } from 'react';\nimport './Countdown.css';\n\nconst getTimeLeft = (endDateTime) => {\n    const total = Date.parse(endDateTime) - Date.now();\n    const seconds = Math.floor((total / 1000) % 60);\n    const minutes = Math.floor((total / 1000 / 60) % 60);\n    const hours = Math.floor((total / (1000 * 60 * 60)) % 24);\n    const days = Math.floor(total / (1000 * 60 * 60 * 24));\n    return { total, days, hours, minutes, seconds };\n};\n\nconst Countdown = ({ endDateTime }) => {\n    const [timeLeft, setTimeLeft] = useState(getTimeLeft(endDateTime));\n\n    useEffect(() => {\n        const timer = setInterval(() => {\n            setTimeLeft(getTimeLeft(endDateTime));\n        }, 1000);\n        return () => clearInterval(timer);\n    }, [endDateTime]);\n\n    if (timeLeft.total <= 0) {\n        return <div className=\"amlak-countdown\">انتهى المزاد</div>;\n    }\n\n    // تصميم العداد كبطاقات مستطيلة أفقية فقط\n    return (\n        <div className=\"amlak-countdown-cards-row\">\n            <div className=\"countdown-rects\">\n                <div className=\"countdown-rect\"><div className=\"countdown-num\">{timeLeft.days}</div><div className=\"countdown-label\">يوم</div></div>\n                <div className=\"countdown-rect\"><div className=\"countdown-num\">{timeLeft.hours}</div><div className=\"countdown-label\">ساعة</div></div>\n                <div className=\"countdown-rect\"><div className=\"countdown-num\">{timeLeft.minutes}</div><div className=\"countdown-label\">دقيقة</div></div>\n                <div className=\"countdown-rect\"><div className=\"countdown-num\">{timeLeft.seconds}</div><div className=\"countdown-label\">ثانية</div></div>\n            </div>\n        </div>\n    );\n};\n\nexport default Countdown;\n", "// extracted by mini-css-extract-plugin\nexport {};", "import React, { useState, useEffect, useRef } from 'react';\nimport './ImgSlider.css';\n\nconst AUTO_SLIDE_INTERVAL = 3000; // 3 ثواني\n\nconst ImgSlider = ({ images = [] }) => {\n    const [current, setCurrent] = useState(0);\n    const [paused, setPaused] = useState(false);\n    const intervalRef = useRef();\n    if (!images.length) return null;\n\n    // التقليب التلقائي\n    useEffect(() => {\n        if (paused) return;\n        intervalRef.current = setInterval(() => {\n            setCurrent((prev) => (prev + 1) % images.length);\n        }, AUTO_SLIDE_INTERVAL);\n        return () => clearInterval(intervalRef.current);\n    }, [images.length, paused]);\n\n    // عند تمرير الماوس توقف التقليب\n    const handleMouseEnter = () => setPaused(true);\n    const handleMouseLeave = () => setPaused(false);\n\n    return (\n        <div className=\"amlak-img-slider\" onMouseEnter={handleMouseEnter} onMouseLeave={handleMouseLeave}>\n            <div className=\"slider-img-fade-wrapper\">\n                {images.map((img, idx) => (\n                    <img\n                        key={idx}\n                        src={img}\n                        alt=\"صورة المزاد\"\n                        className={\n                            'slider-img-fade' + (idx === current ? ' active' : '')\n                        }\n                        style={{ zIndex: idx === current ? 2 : 1 }}\n                    />\n                ))}\n            </div>\n            <div className=\"slider-dots\">\n                {images.map((_, idx) => (\n                    <span\n                        key={idx}\n                        className={idx === current ? 'dot active' : 'dot'}\n                        onClick={() => setCurrent(idx)}\n                    />\n                ))}\n            </div>\n        </div>\n    );\n};\n\nexport default ImgSlider;\n", "import React from 'react';\nimport { PanelBody, ToggleControl, RangeControl } from '@wordpress/components';\n\nconst SettingsPanel = ({ attributes, setAttributes }) => {\n    return (\n        <PanelBody title=\"إعدادات عرض المزادات\" initialOpen={true}>\n            <RangeControl\n                label=\"عدد الأعمدة في كل صف\"\n                value={attributes.columns}\n                onChange={(val) => setAttributes({ columns: val })}\n                min={1}\n                max={6}\n            />\n            <RangeControl\n                label=\"عدد المزادات المعروضة\"\n                value={attributes.auctionsCount}\n                onChange={(val) => setAttributes({ auctionsCount: val })}\n                min={1}\n                max={30}\n            />\n            <ToggleControl\n                label=\"إظهار الصور\"\n                checked={attributes.showImages}\n                onChange={(val) => setAttributes({ showImages: val })}\n            />\n            <ToggleControl\n                label=\"إظهار اسم المزاد\"\n                checked={attributes.showTitle}\n                onChange={(val) => setAttributes({ showTitle: val })}\n            />\n            <ToggleControl\n                label=\"إظهار نوع المزاد\"\n                checked={attributes.showType}\n                onChange={(val) => setAttributes({ showType: val })}\n            />\n            <ToggleControl\n                label=\"إظهار المكان\"\n                checked={attributes.showLocation}\n                onChange={(val) => setAttributes({ showLocation: val })}\n            />\n            <ToggleControl\n                label=\"إظهار عدد الأصول\"\n                checked={attributes.showAssetsCount}\n                onChange={(val) => setAttributes({ showAssetsCount: val })}\n            />\n            <ToggleControl\n                label=\"إظهار التاريخ والوقت\"\n                checked={attributes.showDateTime}\n                onChange={(val) => setAttributes({ showDateTime: val })}\n            />\n            <ToggleControl\n                label=\"إظهار العداد التنازلي\"\n                checked={attributes.showCountdown}\n                onChange={(val) => setAttributes({ showCountdown: val })}\n            />\n            <ToggleControl\n                label=\"إظهار اسم الوكيل\"\n                checked={attributes.showAgent}\n                onChange={(val) => setAttributes({ showAgent: val })}\n            />\n        </PanelBody>\n    );\n};\n\nexport default SettingsPanel;\n", "import React, { useEffect, useState } from 'react';\nimport { InspectorControls, useBlockProps } from '@wordpress/block-editor';\nimport Card from './Card';\nimport SettingsPanel from './SettingsPanel';\nimport './style.scss';\n\nconst mockAuctions = [\n    {\n        id: 1,\n        title: 'مزاد الرياض للعقارات',\n        type: 'إلكتروني',\n        location: 'الرياض - العليا',\n        assetsCount: 5,\n        date: '2025-06-30',\n        time: '17:39',\n        endDateTime: '2025-06-30T17:39:00',\n        agent: 'شركة أملـاك',\n        images: [\n            'https://images.unsplash.com/photo-1506744038136-46273834b3fb?auto=format&fit=crop&w=400&q=80',\n            'https://images.unsplash.com/photo-1465101046530-73398c7f28ca?auto=format&fit=crop&w=400&q=80',\n        ],\n    },\n    {\n        id: 2,\n        title: 'مز<PERSON> جدة للأراضي',\n        type: 'حضوري',\n        location: 'جدة - التحلية',\n        assetsCount: 3,\n        date: '2025-07-10',\n        time: '15:00',\n        endDateTime: '2025-07-10T15:00:00',\n        agent: 'مؤسسة جدة',\n        images: [\n            'https://images.unsplash.com/photo-1512918728675-ed5a9ecdebfd?auto=format&fit=crop&w=400&q=80',\n            'https://images.unsplash.com/photo-1523217582562-09d0def993a6?auto=format&fit=crop&w=400&q=80',\n        ],\n    },\n    {\n        id: 3,\n        title: 'مزاد الدمام للفلل',\n        type: 'هجين',\n        location: 'الدمام - الخليج',\n        assetsCount: 7,\n        date: '2025-08-15',\n        time: '18:30',\n        endDateTime: '2025-08-15T18:30:00',\n        agent: 'وكالة الشرقية',\n        images: [\n            'https://images.unsplash.com/photo-1468436139062-f60a71c5c892?auto=format&fit=crop&w=400&q=80',\n            'https://images.unsplash.com/photo-1507089947368-19c1da9775ae?auto=format&fit=crop&w=400&q=80',\n        ],\n    },\n];\n\nconst AuctionsGrid = ({ auctions, columns, settings }) => (\n    <div\n        className=\"amlak-auctions-grid\"\n        style={{\n            display: 'grid',\n            gridTemplateColumns: `repeat(${columns}, 1fr)`,\n            gap: '24px',\n        }}\n    >\n        {auctions.map((auction) => (\n            <Card key={auction.id} auction={auction} settings={settings} />\n        ))}\n    </div>\n);\n\nexport default function Edit({ attributes, setAttributes }) {\n    // لاحقاً: استبدل mockAuctions بجلب فعلي من REST API\n    const [auctions, setAuctions] = useState(mockAuctions.slice(0, attributes.auctionsCount));\n\n    useEffect(() => {\n        setAuctions(mockAuctions.slice(0, attributes.auctionsCount));\n    }, [attributes.auctionsCount]);\n\n    return (\n        <div {...useBlockProps()}>\n            <InspectorControls>\n                <SettingsPanel attributes={attributes} setAttributes={setAttributes} />\n            </InspectorControls>\n            <AuctionsGrid auctions={auctions} columns={attributes.columns} settings={attributes} />\n        </div>\n    );\n}\n", "/**\n * Registers a new block provided a unique name and an object defining its behavior.\n *\n * @see https://developer.wordpress.org/block-editor/reference-guides/block-api/block-registration/\n */\nimport { registerBlockType } from '@wordpress/blocks';\n\n/**\n * Lets webpack process CSS, SASS or SCSS files referenced in JavaScript files.\n * All files containing `style` keyword are bundled together. The code used\n * gets applied both to the front of your site and to the editor.\n *\n * @see https://www.npmjs.com/package/@wordpress/scripts#using-css\n */\nimport './style.scss';\n\n/**\n * Internal dependencies\n */\nimport Edit from './edit';\nimport save from './save';\nimport metadata from './block.json';\n\n/**\n * Every block starts by registering a new block type definition.\n *\n * @see https://developer.wordpress.org/block-editor/reference-guides/block-api/block-registration/\n */\nregisterBlockType( metadata.name, {\n\t/**\n\t * @see ./edit.js\n\t */\n\tedit: Edit,\n\n\t/**\n\t * @see ./save.js\n\t */\n\tsave,\n} );\n", "/**\n * React hook that is used to mark the block wrapper element.\n * It provides all the necessary props like the class name.\n *\n * @see https://developer.wordpress.org/block-editor/reference-guides/packages/packages-block-editor/#useblockprops\n */\nimport { useBlockProps } from '@wordpress/block-editor';\n\n/**\n * The save function defines the way in which the different attributes should\n * be combined into the final markup, which is then serialized by the block\n * editor into `post_content`.\n *\n * @see https://developer.wordpress.org/block-editor/reference-guides/block-api/block-edit-save/#save\n *\n * @return {Element} Element to render.\n */\nexport default function save() {\n\treturn (\n\t\t<p { ...useBlockProps.save() }>\n\t\t\t{ 'Amlak Auctions Block – hello from the saved content!' }\n\t\t</p>\n\t);\n}\n", "// extracted by mini-css-extract-plugin\nexport {};", "module.exports = window[\"wp\"][\"blockEditor\"];", "module.exports = window[\"wp\"][\"blocks\"];", "module.exports = window[\"wp\"][\"components\"];", "module.exports = window[\"React\"];", "module.exports = window[\"ReactJSXRuntime\"];", "// The module cache\nvar __webpack_module_cache__ = {};\n\n// The require function\nfunction __webpack_require__(moduleId) {\n\t// Check if module is in cache\n\tvar cachedModule = __webpack_module_cache__[moduleId];\n\tif (cachedModule !== undefined) {\n\t\treturn cachedModule.exports;\n\t}\n\t// Create a new module (and put it into the cache)\n\tvar module = __webpack_module_cache__[moduleId] = {\n\t\t// no module.id needed\n\t\t// no module.loaded needed\n\t\texports: {}\n\t};\n\n\t// Execute the module function\n\t__webpack_modules__[moduleId](module, module.exports, __webpack_require__);\n\n\t// Return the exports of the module\n\treturn module.exports;\n}\n\n// expose the modules object (__webpack_modules__)\n__webpack_require__.m = __webpack_modules__;\n\n", "var deferred = [];\n__webpack_require__.O = (result, chunkIds, fn, priority) => {\n\tif(chunkIds) {\n\t\tpriority = priority || 0;\n\t\tfor(var i = deferred.length; i > 0 && deferred[i - 1][2] > priority; i--) deferred[i] = deferred[i - 1];\n\t\tdeferred[i] = [chunkIds, fn, priority];\n\t\treturn;\n\t}\n\tvar notFulfilled = Infinity;\n\tfor (var i = 0; i < deferred.length; i++) {\n\t\tvar [chunkIds, fn, priority] = deferred[i];\n\t\tvar fulfilled = true;\n\t\tfor (var j = 0; j < chunkIds.length; j++) {\n\t\t\tif ((priority & 1 === 0 || notFulfilled >= priority) && Object.keys(__webpack_require__.O).every((key) => (__webpack_require__.O[key](chunkIds[j])))) {\n\t\t\t\tchunkIds.splice(j--, 1);\n\t\t\t} else {\n\t\t\t\tfulfilled = false;\n\t\t\t\tif(priority < notFulfilled) notFulfilled = priority;\n\t\t\t}\n\t\t}\n\t\tif(fulfilled) {\n\t\t\tdeferred.splice(i--, 1)\n\t\t\tvar r = fn();\n\t\t\tif (r !== undefined) result = r;\n\t\t}\n\t}\n\treturn result;\n};", "// getDefaultExport function for compatibility with non-harmony modules\n__webpack_require__.n = (module) => {\n\tvar getter = module && module.__esModule ?\n\t\t() => (module['default']) :\n\t\t() => (module);\n\t__webpack_require__.d(getter, { a: getter });\n\treturn getter;\n};", "// define getter functions for harmony exports\n__webpack_require__.d = (exports, definition) => {\n\tfor(var key in definition) {\n\t\tif(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n\t\t\tObject.defineProperty(exports, key, { enumerable: true, get: definition[key] });\n\t\t}\n\t}\n};", "__webpack_require__.o = (obj, prop) => (Object.prototype.hasOwnProperty.call(obj, prop))", "// define __esModule on exports\n__webpack_require__.r = (exports) => {\n\tif(typeof Symbol !== 'undefined' && Symbol.toStringTag) {\n\t\tObject.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });\n\t}\n\tObject.defineProperty(exports, '__esModule', { value: true });\n};", "// no baseURI\n\n// object to store loaded and loading chunks\n// undefined = chunk not loaded, null = chunk preloaded/prefetched\n// [resolve, reject, Promise] = chunk loading, 0 = chunk loaded\nvar installedChunks = {\n\t\"amlak-auctions-block/index\": 0,\n\t\"amlak-auctions-block/style-index\": 0\n};\n\n// no chunk on demand loading\n\n// no prefetching\n\n// no preloaded\n\n// no HMR\n\n// no HMR manifest\n\n__webpack_require__.O.j = (chunkId) => (installedChunks[chunkId] === 0);\n\n// install a JSONP callback for chunk loading\nvar webpackJsonpCallback = (parentChunkLoadingFunction, data) => {\n\tvar [chunkIds, moreModules, runtime] = data;\n\t// add \"moreModules\" to the modules object,\n\t// then flag all \"chunkIds\" as loaded and fire callback\n\tvar moduleId, chunkId, i = 0;\n\tif(chunkIds.some((id) => (installedChunks[id] !== 0))) {\n\t\tfor(moduleId in moreModules) {\n\t\t\tif(__webpack_require__.o(moreModules, moduleId)) {\n\t\t\t\t__webpack_require__.m[moduleId] = moreModules[moduleId];\n\t\t\t}\n\t\t}\n\t\tif(runtime) var result = runtime(__webpack_require__);\n\t}\n\tif(parentChunkLoadingFunction) parentChunkLoadingFunction(data);\n\tfor(;i < chunkIds.length; i++) {\n\t\tchunkId = chunkIds[i];\n\t\tif(__webpack_require__.o(installedChunks, chunkId) && installedChunks[chunkId]) {\n\t\t\tinstalledChunks[chunkId][0]();\n\t\t}\n\t\tinstalledChunks[chunkId] = 0;\n\t}\n\treturn __webpack_require__.O(result);\n}\n\nvar chunkLoadingGlobal = globalThis[\"webpackChunkamlak_auctions_block\"] = globalThis[\"webpackChunkamlak_auctions_block\"] || [];\nchunkLoadingGlobal.forEach(webpackJsonpCallback.bind(null, 0));\nchunkLoadingGlobal.push = webpackJsonpCallback.bind(null, chunkLoadingGlobal.push.bind(chunkLoadingGlobal));", "", "// startup\n// Load entry module and return exports\n// This entry module depends on other loaded chunks and execution need to be delayed\nvar __webpack_exports__ = __webpack_require__.O(undefined, [\"amlak-auctions-block/style-index\"], () => (__webpack_require__(\"./src/amlak-auctions-block/index.js\")))\n__webpack_exports__ = __webpack_require__.O(__webpack_exports__);\n", ""], "names": ["React", "formatAuctionDateTime", "date", "time", "months", "monthsShort", "year", "month", "day", "split", "monthName", "parseInt", "result", "length", "ImgSlider", "Countdown", "jsx", "_jsx", "jsxs", "_jsxs", "Card", "auction", "settings", "auctionTypeIcon", "width", "height", "viewBox", "fill", "children", "x", "y", "rx", "d", "style", "display", "alignItems", "gap", "className", "showImages", "images", "showType", "type", "showTitle", "title", "showAgent", "agent", "showLocation", "location", "showAssetsCount", "assetsCount", "showDateTime", "showCountdown", "endDateTime", "useEffect", "useState", "getTimeLeft", "total", "Date", "parse", "now", "seconds", "Math", "floor", "minutes", "hours", "days", "timeLeft", "setTimeLeft", "timer", "setInterval", "clearInterval", "useRef", "AUTO_SLIDE_INTERVAL", "current", "setCurrent", "paused", "setPaused", "intervalRef", "prev", "handleMouseEnter", "handleMouseLeave", "onMouseEnter", "onMouseLeave", "map", "img", "idx", "src", "alt", "zIndex", "_", "onClick", "PanelBody", "ToggleControl", "RangeControl", "SettingsPanel", "attributes", "setAttributes", "initialOpen", "label", "value", "columns", "onChange", "val", "min", "max", "auctionsCount", "checked", "InspectorCont<PERSON><PERSON>", "useBlockProps", "mockAuctions", "id", "AuctionsGrid", "auctions", "gridTemplateColumns", "Edit", "setAuctions", "slice", "registerBlockType", "save", "metadata", "name", "edit"], "sourceRoot": ""}